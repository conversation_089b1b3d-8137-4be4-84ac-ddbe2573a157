# Azure AKS Connection Guide

This guide helps you connect to your Azure Kubernetes Service (AKS) cluster.

## Your Cluster Details

- **Subscription ID**: `3c6373fe-834d-4f88-bb56-539b8e02bd96`
- **Resource Group**: `RG-SEA-EAPI-POC-001`
- **Cluster Name**: `aks-n8n`
- **Azure Portal URL**: [View in Azure Portal](https://app.azure.com/45202dee-4088-4e8c-8ebd-c01f56740e8f/subscriptions/3c6373fe-834d-4f88-bb56-539b8e02bd96/resourcegroups/RG-SEA-EAPI-POC-001/providers/Microsoft.ContainerService/managedClusters/aks-n8n)

## Prerequisites

### 1. Install Azure CLI

**Option A: Download and Install**
1. Download from: https://aka.ms/installazurecliwindows
2. Run the installer
3. Restart PowerShell/Command Prompt

**Option B: Using winget (if available)**
```powershell
winget install Microsoft.AzureCLI
```

**Option C: Using Chocolatey (if available)**
```powershell
choco install azure-cli
```

### 2. kubectl (Already Downloaded)

✅ `kubectl.exe` is already available in this directory.

## Quick Connection

### Automated Script
Run the PowerShell script that handles everything for you:

```powershell
.\connect-to-aks.ps1
```

### Manual Steps

If you prefer to run the commands manually:

1. **Login to Azure**
   ```powershell
   az login
   ```

2. **Set your subscription**
   ```powershell
   az account set --subscription 3c6373fe-834d-4f88-bb56-539b8e02bd96
   ```

3. **Get AKS credentials**
   ```powershell
   az aks get-credentials --resource-group RG-SEA-EAPI-POC-001 --name aks-n8n
   ```

4. **Verify connection**
   ```powershell
   .\kubectl.exe get nodes
   ```

## Common kubectl Commands

Once connected, you can use these commands:

```powershell
# List all nodes in the cluster
.\kubectl.exe get nodes

# List all pods in all namespaces
.\kubectl.exe get pods --all-namespaces

# List all services
.\kubectl.exe get services

# List all namespaces
.\kubectl.exe get namespaces

# Get cluster information
.\kubectl.exe cluster-info

# Check if n8n is already deployed
.\kubectl.exe get pods -l app=n8n

# Check for existing deployments
.\kubectl.exe get deployments
```

## Troubleshooting

### Azure CLI not found
- Make sure Azure CLI is installed
- Restart your PowerShell session
- Check if `az --version` works

### kubectl not working
- Use `.\kubectl.exe` instead of just `kubectl`
- Make sure you're in the correct directory

### Access denied
- Verify you have the correct permissions on the Azure subscription
- Check if you're logged in with the correct account: `az account show`

### Cluster not found
- Verify the cluster name and resource group are correct
- Check if the cluster exists in the Azure portal

## Next Steps

After connecting successfully, you can:
1. Deploy n8n to your AKS cluster
2. Set up ingress controllers
3. Configure persistent storage
4. Set up monitoring and logging

## Files in this directory

- `connect-to-aks.ps1` - Automated connection script
- `kubectl.exe` - Kubernetes command-line tool
- `README.md` - This guide

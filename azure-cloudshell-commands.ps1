# Azure Cloud Shell Commands for AKS Connection (PowerShell)
# Copy and paste these commands into Azure Cloud Shell (PowerShell mode)

Write-Host "🌐 Azure AKS Connection via Cloud Shell" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Set your subscription (already authenticated in Cloud Shell)
Write-Host "📋 Setting subscription..." -ForegroundColor Blue
az account set --subscription 3c6373fe-834d-4f88-bb56-539b8e02bd96

# Verify subscription is set
Write-Host "✅ Current subscription:" -ForegroundColor Green
az account show --query "{name:name, id:id}" --output table

# Get AKS credentials
Write-Host "🔑 Getting AKS credentials..." -ForegroundColor Blue
az aks get-credentials --resource-group RG-SEA-EAPI-POC-001 --name aks-n8n

# Verify connection
Write-Host "🔍 Verifying cluster connection..." -ForegroundColor Blue
kubectl get nodes

Write-Host ""
Write-Host "🎉 If you see nodes listed above, you're successfully connected!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Common commands you can now run:" -ForegroundColor Yellow
Write-Host "   kubectl get nodes                    # List cluster nodes"
Write-Host "   kubectl get pods --all-namespaces   # List all pods"
Write-Host "   kubectl get services                # List services"
Write-Host "   kubectl get namespaces              # List namespaces"
Write-Host "   kubectl cluster-info                # Get cluster information"
Write-Host ""
Write-Host "🔍 Check if n8n is already deployed:" -ForegroundColor Yellow
Write-Host "   kubectl get pods -l app=n8n"
Write-Host "   kubectl get deployments"
Write-Host "   kubectl get services -l app=n8n"

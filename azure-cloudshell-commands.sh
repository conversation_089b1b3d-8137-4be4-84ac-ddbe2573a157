#!/bin/bash
# Azure Cloud Shell Commands for AKS Connection
# Copy and paste these commands into Azure Cloud Shell

echo "🌐 Azure AKS Connection via Cloud Shell"
echo "======================================="

# Set your subscription (already authenticated in Cloud Shell)
echo "📋 Setting subscription..."
az account set --subscription 3c6373fe-834d-4f88-bb56-539b8e02bd96

# Verify subscription is set
echo "✅ Current subscription:"
az account show --query "{name:name, id:id}" --output table

# Get AKS credentials
echo "🔑 Getting AKS credentials..."
az aks get-credentials --resource-group RG-SEA-EAPI-POC-001 --name aks-n8n

# Verify connection
echo "🔍 Verifying cluster connection..."
kubectl get nodes

echo ""
echo "🎉 If you see nodes listed above, you're successfully connected!"
echo ""
echo "📝 Common commands you can now run:"
echo "   kubectl get nodes                    # List cluster nodes"
echo "   kubectl get pods --all-namespaces   # List all pods"
echo "   kubectl get services                # List services"
echo "   kubectl get namespaces              # List namespaces"
echo "   kubectl cluster-info                # Get cluster information"
echo ""
echo "🔍 Check if n8n is already deployed:"
echo "   kubectl get pods -l app=n8n"
echo "   kubectl get deployments"
echo "   kubectl get services -l app=n8n"

# Deploy n8n to Azure AKS

This guide shows you how to deploy n8n (workflow automation tool) to your Azure Kubernetes Service cluster.

## Prerequisites

1. Connected to your AKS cluster (use the Cloud Shell commands)
2. kubectl working and authenticated

## Quick Deployment Options

### Option 1: Simple n8n Deployment

Create a basic n8n deployment:

```bash
# Create n8n namespace
kubectl create namespace n8n

# Deploy n8n
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
  namespace: n8n
  labels:
    app: n8n
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n
  template:
    metadata:
      labels:
        app: n8n
    spec:
      containers:
      - name: n8n
        image: n8nio/n8n:latest
        ports:
        - containerPort: 5678
        env:
        - name: N8N_HOST
          value: "0.0.0.0"
        - name: N8N_PORT
          value: "5678"
        - name: N8N_PROTOCOL
          value: "http"
        - name: WEBHOOK_URL
          value: "http://your-domain.com"
        volumeMounts:
        - name: n8n-data
          mountPath: /home/<USER>/.n8n
      volumes:
      - name: n8n-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: n8n-service
  namespace: n8n
  labels:
    app: n8n
spec:
  selector:
    app: n8n
  ports:
  - port: 80
    targetPort: 5678
    protocol: TCP
  type: LoadBalancer
EOF
```

### Option 2: Using Helm (Recommended)

```bash
# Add n8n Helm repository
helm repo add n8n https://8gears.container-registry.com/chartrepo/library
helm repo update

# Install n8n with Helm
helm install n8n n8n/n8n \
  --namespace n8n \
  --create-namespace \
  --set service.type=LoadBalancer \
  --set persistence.enabled=true \
  --set persistence.size=10Gi
```

## Configuration Options

### Environment Variables

Common n8n environment variables:

```yaml
env:
- name: N8N_BASIC_AUTH_ACTIVE
  value: "true"
- name: N8N_BASIC_AUTH_USER
  value: "admin"
- name: N8N_BASIC_AUTH_PASSWORD
  value: "your-secure-password"
- name: N8N_HOST
  value: "0.0.0.0"
- name: N8N_PORT
  value: "5678"
- name: N8N_PROTOCOL
  value: "https"
- name: WEBHOOK_URL
  value: "https://your-n8n-domain.com"
```

### Persistent Storage

For production, use persistent storage:

```yaml
volumeMounts:
- name: n8n-data
  mountPath: /home/<USER>/.n8n
volumes:
- name: n8n-data
  persistentVolumeClaim:
    claimName: n8n-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: n8n-pvc
  namespace: n8n
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: managed-csi
```

## Accessing n8n

### Check Deployment Status

```bash
# Check if pods are running
kubectl get pods -n n8n

# Check service status
kubectl get services -n n8n

# Get external IP (for LoadBalancer)
kubectl get service n8n-service -n n8n -o jsonpath='{.status.loadBalancer.ingress[0].ip}'
```

### Access Methods

1. **LoadBalancer (External IP)**
   - Wait for external IP to be assigned
   - Access via: `http://EXTERNAL-IP`

2. **Port Forward (for testing)**
   ```bash
   kubectl port-forward -n n8n service/n8n-service 8080:80
   ```
   - Access via: `http://localhost:8080`

3. **Ingress Controller** (for production with custom domain)

## Security Considerations

### 1. Enable Basic Authentication

```yaml
env:
- name: N8N_BASIC_AUTH_ACTIVE
  value: "true"
- name: N8N_BASIC_AUTH_USER
  value: "admin"
- name: N8N_BASIC_AUTH_PASSWORD
  valueFrom:
    secretKeyRef:
      name: n8n-auth
      key: password
```

### 2. Use HTTPS

Set up an ingress controller with TLS:

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: n8n-ingress
  namespace: n8n
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - n8n.yourdomain.com
    secretName: n8n-tls
  rules:
  - host: n8n.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: n8n-service
            port:
              number: 80
```

## Monitoring and Maintenance

### Check Logs

```bash
# View n8n logs
kubectl logs -n n8n deployment/n8n -f

# View all events in namespace
kubectl get events -n n8n --sort-by='.lastTimestamp'
```

### Scale n8n

```bash
# Scale to multiple replicas (be careful with data persistence)
kubectl scale deployment n8n -n n8n --replicas=2
```

### Update n8n

```bash
# Update to latest version
kubectl set image deployment/n8n -n n8n n8n=n8nio/n8n:latest

# Or with Helm
helm upgrade n8n n8n/n8n -n n8n
```

## Troubleshooting

### Common Issues

1. **Pod not starting**
   ```bash
   kubectl describe pod -n n8n -l app=n8n
   ```

2. **Service not accessible**
   ```bash
   kubectl get endpoints -n n8n
   ```

3. **Storage issues**
   ```bash
   kubectl get pvc -n n8n
   kubectl describe pvc n8n-pvc -n n8n
   ```

### Cleanup

```bash
# Remove n8n deployment
kubectl delete namespace n8n

# Or with Helm
helm uninstall n8n -n n8n
```
